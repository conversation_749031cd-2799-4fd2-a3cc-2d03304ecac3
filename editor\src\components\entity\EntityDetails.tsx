/**
 * 实体详情组件
 * 用于显示和编辑选中实体的详细信息
 */
import React, { useState } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Switch, 
  Button, 
  Space, 
  Divider, 
  Typography, 
  Collapse,
  InputNumber,
  Select,
  Tag,
  Empty
} from 'antd';
import {
  EditOutlined,
  SaveOutlined,
  UndoOutlined,
  PlusOutlined,
  DeleteOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { updateEntity } from '../../store/scene/sceneSlice';

const { Text, Title } = Typography;
const { Panel } = Collapse;
const { Option } = Select;

/**
 * 实体详情组件属性
 */
interface EntityDetailsProps {
  /** 实体ID */
  entityId: string;
}

/**
 * 实体详情组件
 */
export const EntityDetails: React.FC<EntityDetailsProps> = ({ entityId }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  
  // 获取实体数据
  const entity = useSelector((state: RootState) => 
    state.scene?.entities?.find(e => e.id === entityId)
  );
  
  // 本地状态
  const [isEditing, setIsEditing] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  
  // 如果没有实体，显示空状态
  if (!entity) {
    return (
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description={t('editor.common.noEntitySelected')}
        style={{ padding: '40px 20px' }}
      />
    );
  }
  
  // 处理表单值变化
  const handleFormChange = () => {
    setHasChanges(true);
  };
  
  // 保存更改
  const handleSave = () => {
    form.validateFields().then(values => {
      dispatch(updateEntity({
        id: entityId,
        changes: values
      }));
      setIsEditing(false);
      setHasChanges(false);
    });
  };
  
  // 取消编辑
  const handleCancel = () => {
    form.resetFields();
    setIsEditing(false);
    setHasChanges(false);
  };
  
  // 渲染基本信息
  const renderBasicInfo = () => (
    <Panel header={t('editor.common.basicInfo')} key="basic">
      <Form
        form={form}
        layout="vertical"
        initialValues={entity}
        onValuesChange={handleFormChange}
        disabled={!isEditing}
      >
        <Form.Item
          name="name"
          label={t('editor.common.name')}
          rules={[{ required: true, message: t('editor.common.nameRequired') }]}
        >
          <Input placeholder={t('editor.common.enterName')} />
        </Form.Item>
        
        <Form.Item
          name="type"
          label={t('editor.common.type')}
        >
          <Select disabled>
            <Option value="mesh">Mesh</Option>
            <Option value="light">Light</Option>
            <Option value="camera">Camera</Option>
            <Option value="empty">Empty</Option>
            <Option value="terrain">Terrain</Option>
          </Select>
        </Form.Item>
        
        <Form.Item
          name="visible"
          label={t('editor.common.visible')}
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
        
        <Form.Item
          name="locked"
          label={t('editor.common.locked')}
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
      </Form>
    </Panel>
  );
  
  // 渲染变换信息
  const renderTransformInfo = () => (
    <Panel header={t('editor.common.transform')} key="transform">
      <Form
        layout="vertical"
        initialValues={entity.transform}
        disabled={!isEditing}
      >
        <Text strong>{t('editor.common.position')}</Text>
        <Space.Compact style={{ width: '100%', marginBottom: 16 }}>
          <InputNumber
            placeholder="X"
            value={entity.transform?.position?.[0] || 0}
            style={{ width: '33.33%' }}
          />
          <InputNumber
            placeholder="Y"
            value={entity.transform?.position?.[1] || 0}
            style={{ width: '33.33%' }}
          />
          <InputNumber
            placeholder="Z"
            value={entity.transform?.position?.[2] || 0}
            style={{ width: '33.33%' }}
          />
        </Space.Compact>
        
        <Text strong>{t('editor.common.rotation')}</Text>
        <Space.Compact style={{ width: '100%', marginBottom: 16 }}>
          <InputNumber
            placeholder="X"
            value={entity.transform?.rotation?.[0] || 0}
            style={{ width: '33.33%' }}
          />
          <InputNumber
            placeholder="Y"
            value={entity.transform?.rotation?.[1] || 0}
            style={{ width: '33.33%' }}
          />
          <InputNumber
            placeholder="Z"
            value={entity.transform?.rotation?.[2] || 0}
            style={{ width: '33.33%' }}
          />
        </Space.Compact>
        
        <Text strong>{t('editor.common.scale')}</Text>
        <Space.Compact style={{ width: '100%' }}>
          <InputNumber
            placeholder="X"
            value={entity.transform?.scale?.[0] || 1}
            style={{ width: '33.33%' }}
          />
          <InputNumber
            placeholder="Y"
            value={entity.transform?.scale?.[1] || 1}
            style={{ width: '33.33%' }}
          />
          <InputNumber
            placeholder="Z"
            value={entity.transform?.scale?.[2] || 1}
            style={{ width: '33.33%' }}
          />
        </Space.Compact>
      </Form>
    </Panel>
  );
  
  // 渲染组件信息
  const renderComponentsInfo = () => (
    <Panel header={t('editor.common.components')} key="components">
      {entity.components && Object.keys(entity.components).length > 0 ? (
        <Space direction="vertical" style={{ width: '100%' }}>
          {Object.keys(entity.components).map(componentType => (
            <Card
              key={componentType}
              size="small"
              title={
                <Space>
                  <Text>{componentType}</Text>
                  <Tag size="small">Component</Tag>
                </Space>
              }
              extra={
                <Button
                  type="text"
                  size="small"
                  icon={<SettingOutlined />}
                />
              }
            >
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {t('editor.common.componentConfigured')}
              </Text>
            </Card>
          ))}
        </Space>
      ) : (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={t('editor.common.noComponents')}
          style={{ padding: '20px 0' }}
        >
          <Button
            type="primary"
            size="small"
            icon={<PlusOutlined />}
            disabled={!isEditing}
          >
            {t('editor.common.addComponent')}
          </Button>
        </Empty>
      )}
    </Panel>
  );
  
  return (
    <div className="entity-details">
      {/* 头部 */}
      <div style={{ 
        padding: '12px 16px', 
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          <Title level={5} style={{ margin: 0 }}>
            {entity.name}
          </Title>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {entity.type} • ID: {entity.id}
          </Text>
        </div>
        
        <Space>
          {isEditing ? (
            <>
              <Button
                size="small"
                icon={<UndoOutlined />}
                onClick={handleCancel}
              >
                {t('editor.common.cancel')}
              </Button>
              <Button
                type="primary"
                size="small"
                icon={<SaveOutlined />}
                onClick={handleSave}
                disabled={!hasChanges}
              >
                {t('editor.common.save')}
              </Button>
            </>
          ) : (
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => setIsEditing(true)}
            >
              {t('editor.common.edit')}
            </Button>
          )}
        </Space>
      </div>
      
      {/* 内容 */}
      <div style={{ padding: '16px' }}>
        <Collapse defaultActiveKey={['basic', 'transform', 'components']}>
          {renderBasicInfo()}
          {renderTransformInfo()}
          {renderComponentsInfo()}
        </Collapse>
      </div>
    </div>
  );
};

export default EntityDetails;
